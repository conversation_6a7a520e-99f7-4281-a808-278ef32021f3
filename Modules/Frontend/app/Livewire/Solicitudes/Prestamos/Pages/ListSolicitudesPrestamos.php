<?php

namespace Modules\Frontend\Livewire\Solicitudes\Prestamos\Pages;

use App\Models\AsociadosSolicitudesPrestamos;
use Rappasoft\LaravelLivewireTables\DataTableComponent;
use Rappasoft\LaravelLivewireTables\Views\Column;
use Illuminate\Database\Eloquent\Builder;

class ListSolicitudesPrestamos extends DataTableComponent
{
    protected $model = AsociadosSolicitudesPrestamos::class;

    /**
     * Configure the function.
     *
     * @return void
     */
    public function configure(): void
    {
        $this->setPrimaryKey('idsolicitud')
            ->setTableRowUrl(function ($row) {
                return route('dashboard.solicitudes.prestamos.show', $row);
            });
    }

    /**
     * Retrieves the list of columns for the API response.
     *
     * @return array The array of columns for the API response.
     */
    public function columns(): array
    {
        return [
            Column::make("Id", "idsolicitud")
                ->sortable(),
            Column::make("Codigo", "codigo_prest")
                ->searchable()
                ->sortable(),
            Column::make("Préstamo", "prestamo.descripcion")
                ->searchable()
                ->sortable(),
            Column::make("Fecha", "fecha_sol")
                ->searchable()
                ->sortable(),
            Column::make("Monto", "montot_prest")
                ->searchable()
                ->sortable(),
            Column::make("Estatus", "condic_sol")
                ->format(function ($value, $row, Column $column) {
                    return "<span class='badge badge-{$row->condicion->color}'><i class='{$row->condicion->icono}'></i> {$row->condicion->descripcion}</span>";
                })->sortable()
                ->searchable(
                    fn (Builder $query, $searchTerm) => $query->orWhere('config_tools.descripcion', 'like', '%' . $searchTerm . '%')->with('condicion'),
                )->html(),
        ];
    }

    /**
     * Retrieves a Builder instance for querying the AsociadosSolicitudesPrestamos table.
     *
     * @return Builder The Builder instance for querying the AsociadosSolicitudesPrestamos table.
     */
    public function builder(): Builder
    {
        return AsociadosSolicitudesPrestamos::query()
            ->where('asociado_idasociado', auth()->user()->asociado_idasociado)
            ->where('asociados_solicitudes_prestamos.soporte_fi', 1)
            ->join('config_tools', 'config_tools.codigo', '=', 'asociados_solicitudes_prestamos.condic_sol')
            ->orderBy('idsolicitud', 'desc')
            ->distinct('idsolicitud');
    }
}
