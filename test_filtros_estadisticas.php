<?php

require_once 'vendor/autoload.php';

use App\Exports\ContratosVicerrectoradoEstadisticasExport;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    echo "Testing filters in ContratosVicerrectoradoEstadisticasExport...\n";
    
    // Test with filters
    $filters = [
        'estatus' => '1', // Solo contratos activos
        'periodo' => '2024'
    ];
    
    $export = new ContratosVicerrectoradoEstadisticasExport(null, $filters);
    echo "Export with filters instantiated successfully\n";
    
    $collection = $export->collection();
    echo "Collection size with filters: " . $collection->count() . "\n";
    
    echo "\nEstadísticas con filtros (Estatus: Activo, Periodo: 2024):\n";
    echo str_repeat("-", 80) . "\n";
    printf("%-50s %-15s %-15s\n", "VICERRECTORADO", "CONTRATOS", "PORCENTAJE");
    echo str_repeat("-", 80) . "\n";
    
    foreach ($collection as $row) {
        $mapped = $export->map($row);
        printf("%-50s %-15s %-15s\n", $mapped[0], $mapped[1], $mapped[2]);
    }
    
    echo str_repeat("-", 80) . "\n";
    
    // Test without filters
    echo "\nComparing with export without filters...\n";
    $exportSinFiltros = new ContratosVicerrectoradoEstadisticasExport();
    $collectionSinFiltros = $exportSinFiltros->collection();
    echo "Collection size without filters: " . $collectionSinFiltros->count() . "\n";
    
    echo "\nFilter test completed successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
