<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class ContabAuxiliare
 * 
 * @property int $idauxiliar
 * @property string $codigo_emp
 * @property string $periodo_cont
 * @property string $codigo_auxil
 * @property string $descripcion
 * @property string $codigo_cuenta
 * @property string $codigo_grupo
 * @property string $refauxil
 * @property float $saldo_inic
 * @property float $dbm01
 * @property float $dbm02
 * @property float $dbm03
 * @property float $dbm04
 * @property float $dbm05
 * @property float $dbm06
 * @property float $dbm07
 * @property float $dbm08
 * @property float $dbm09
 * @property float $dbm10
 * @property float $dbm11
 * @property float $dbm12
 * @property float $crm01
 * @property float $crm02
 * @property float $crm03
 * @property float $crm04
 * @property float $crm05
 * @property float $crm06
 * @property float $crm07
 * @property float $crm08
 * @property float $crm09
 * @property float $crm10
 * @property float $crm11
 * @property float $crm12
 * @property float $sacm01
 * @property float $sacm02
 * @property float $sacm03
 * @property float $sacm04
 * @property float $sacm05
 * @property float $sacm06
 * @property float $sacm07
 * @property float $sacm08
 * @property float $sacm09
 * @property float $sacm10
 * @property float $sacm11
 * @property float $sacm12
 * @property float $total_presup
 * @property float $deb_presup
 * @property float $cre_presup
 * @property float $saldo_presup
 *
 * @package App\Models
 */
class ContabAuxiliare extends Model
{
	protected $table = 'contab_auxiliares';
	protected $primaryKey = 'idauxiliar';
	public $timestamps = false;

	protected $casts = [
		'saldo_inic' => 'float',
		'dbm01' => 'float',
		'dbm02' => 'float',
		'dbm03' => 'float',
		'dbm04' => 'float',
		'dbm05' => 'float',
		'dbm06' => 'float',
		'dbm07' => 'float',
		'dbm08' => 'float',
		'dbm09' => 'float',
		'dbm10' => 'float',
		'dbm11' => 'float',
		'dbm12' => 'float',
		'crm01' => 'float',
		'crm02' => 'float',
		'crm03' => 'float',
		'crm04' => 'float',
		'crm05' => 'float',
		'crm06' => 'float',
		'crm07' => 'float',
		'crm08' => 'float',
		'crm09' => 'float',
		'crm10' => 'float',
		'crm11' => 'float',
		'crm12' => 'float',
		'sacm01' => 'float',
		'sacm02' => 'float',
		'sacm03' => 'float',
		'sacm04' => 'float',
		'sacm05' => 'float',
		'sacm06' => 'float',
		'sacm07' => 'float',
		'sacm08' => 'float',
		'sacm09' => 'float',
		'sacm10' => 'float',
		'sacm11' => 'float',
		'sacm12' => 'float',
		'total_presup' => 'float',
		'deb_presup' => 'float',
		'cre_presup' => 'float',
		'saldo_presup' => 'float'
	];

	protected $fillable = [
		'codigo_emp',
		'periodo_cont',
		'codigo_auxil',
		'descripcion',
		'codigo_cuenta',
		'codigo_grupo',
		'refauxil',
		'saldo_inic',
		'dbm01',
		'dbm02',
		'dbm03',
		'dbm04',
		'dbm05',
		'dbm06',
		'dbm07',
		'dbm08',
		'dbm09',
		'dbm10',
		'dbm11',
		'dbm12',
		'crm01',
		'crm02',
		'crm03',
		'crm04',
		'crm05',
		'crm06',
		'crm07',
		'crm08',
		'crm09',
		'crm10',
		'crm11',
		'crm12',
		'sacm01',
		'sacm02',
		'sacm03',
		'sacm04',
		'sacm05',
		'sacm06',
		'sacm07',
		'sacm08',
		'sacm09',
		'sacm10',
		'sacm11',
		'sacm12',
		'total_presup',
		'deb_presup',
		'cre_presup',
		'saldo_presup'
	];
}
