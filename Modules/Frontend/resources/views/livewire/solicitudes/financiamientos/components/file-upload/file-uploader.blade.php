<div>
<div
    x-data="{
        isUploading: false,
        progress: 0,
        isDragging: false,
        handleDrop(e) {
            e.preventDefault();
            this.isDragging = false;
            if (e.dataTransfer.files.length) {
                @this.uploadMultiple('files', e.dataTransfer.files);
            }
        },
        handleDragOver(e) {
            e.preventDefault();
            this.isDragging = true;
        },
        handleDragLeave(e) {
            e.preventDefault();
            this.isDragging = false;
        }
    }"
    x-on:livewire-upload-start="isUploading = true"
    x-on:livewire-upload-finish="isUploading = false"
    x-on:livewire-upload-error="isUploading = false"
    x-on:livewire-upload-progress="progress = $event.detail.progress"
    class="file-uploader"
>
    <div class="card card-outline card-primary">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-file-upload mr-2"></i> Documentos requeridos
            </h3>
        </div>
        <div class="card-body">
            <!-- Instrucciones -->
            <div class="alert alert-info mb-3">
                <i class="fas fa-info-circle mr-2"></i> Por favor, adjunte los documentos requeridos para procesar su solicitud de financiamiento.
                <ul class="mt-2 mb-0">
                    <li>Formatos permitidos: {{ $fileTypesDescription }}</li>
                    <li>Tamaño máximo por archivo: {{ $maxSize / 1024 }}MB</li>
                    <li>Máximo {{ $maxFiles }} archivos</li>
                    @if($required)
                    <li><strong>Debe adjuntar al menos un documento para continuar</strong></li>
                    @endif
                </ul>
            </div>

            <!-- Zona de arrastrar y soltar -->
            <div
                class="dropzone-container"
                x-bind:class="{ 'dragging': isDragging }"
                x-on:drop="handleDrop"
                x-on:dragover="handleDragOver"
                x-on:dragleave="handleDragLeave"
            >
                <div class="dropzone-content">
                    <i class="fas fa-cloud-upload-alt fa-3x mb-3 text-primary"></i>
                    <p class="mb-2">Arrastre y suelte los archivos aquí o</p>
                    <label for="file-upload" class="btn btn-primary">
                        <i class="fas fa-folder-open mr-2"></i> Seleccionar archivos
                    </label>
                    <input
                        id="file-upload"
                        type="file"
                        wire:model.live="files"
                        class="hidden-input"
                        multiple
                        accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf"
                    >
                </div>
            </div>

            <!-- Barra de progreso -->
            <div x-show="isUploading" class="progress mt-3">
                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar"
                    x-bind:style="`width: ${progress}%`"
                    x-bind:aria-valuenow="progress"
                    aria-valuemin="0"
                    aria-valuemax="100"
                    x-text="`${progress}%`">
                </div>
            </div>

            <!-- Mensajes de error -->
            @error('files')
                <div class="alert alert-danger mt-3">
                    <ul class="mb-0">
                        <li>{{ $message }}</li>
                    </ul>
                </div>
            @enderror

            @foreach($files as $index => $file)
                @error("files.{$index}")
                    <div class="alert alert-danger mt-3">
                        <ul class="mb-0">
                            <li>{{ $message }}</li>
                        </ul>
                    </div>
                @enderror
            @endforeach

            <!-- Vista previa de archivos -->
            @if (count($files) > 0)
                <div class="file-preview-container mt-3">
                    <h5 class="mb-3">Archivos seleccionados ({{ count($files) }})</h5>
                    <div class="row">
                        @foreach ($files as $index => $file)
                            <div class="col-md-4 col-sm-6 mb-3">
                                <div class="file-preview-card">
                                    <div class="file-preview-header">
                                        <button type="button" class="btn btn-sm btn-danger" wire:click="removeFile({{ $index }})">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                    <div class="file-preview-body">
                                        @if (str_contains($file->getMimeType(), 'image'))
                                            <img src="{{ $file->temporaryUrl() }}" class="img-fluid" alt="{{ $file->getClientOriginalName() }}">
                                        @elseif (str_contains($file->getMimeType(), 'pdf'))
                                            <div class="file-icon">
                                                <i class="far fa-file-pdf fa-4x text-danger"></i>
                                                <p class="mt-2 small text-center">PDF</p>
                                            </div>
                                        @else
                                            <div class="file-icon">
                                                <i class="far fa-file fa-4x text-primary"></i>
                                                <p class="mt-2 small text-center">Archivo</p>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="file-preview-footer">
                                        <p class="file-name" title="{{ $file->getClientOriginalName() }}">
                                            {{ \Illuminate\Support\Str::limit($file->getClientOriginalName(), 20) }}
                                        </p>
                                        <p class="file-size">
                                            {{ round($file->getSize() / 1024, 2) }} KB
                                        </p>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>

                <!-- Botones de acción -->
                <div class="mt-3 text-right">
                    <button type="button" class="btn btn-secondary mr-2" wire:click="resetFiles">
                        <i class="fas fa-trash mr-1"></i> Limpiar archivos
                    </button>
                    <button type="button" class="btn btn-primary" wire:click.prevent="validateFiles">
                        <i class="fas fa-check-circle mr-1"></i> Validar archivos
                    </button>
                </div>
            @endif
        </div>
    </div>
</div>

<style>
    .file-uploader .hidden-input {
        display: none;
    }

    .file-uploader .dropzone-container {
        border: 2px dashed #ccc;
        border-radius: 5px;
        padding: 30px;
        text-align: center;
        transition: all 0.3s ease;
        background-color: #f8f9fa;
    }

    .file-uploader .dropzone-container.dragging {
        background-color: #e9ecef;
        border-color: #007bff;
    }

    .file-uploader .file-preview-card {
        border: 1px solid #ddd;
        border-radius: 5px;
        overflow: hidden;
        height: 100%;
        display: flex;
        flex-direction: column;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .file-uploader .file-preview-header {
        padding: 5px;
        text-align: right;
        background-color: #f8f9fa;
    }

    .file-uploader .file-preview-body {
        flex: 1;
        padding: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 150px;
        background-color: #fff;
    }

    .file-uploader .file-preview-body img {
        max-height: 150px;
        object-fit: contain;
    }

    .file-uploader .file-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
    }

    .file-uploader .file-preview-footer {
        padding: 10px;
        background-color: #f8f9fa;
        border-top: 1px solid #ddd;
    }

    .file-uploader .file-name {
        margin-bottom: 5px;
        font-weight: bold;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .file-uploader .file-size {
        margin-bottom: 0;
        font-size: 12px;
        color: #6c757d;
    }
</style>
</div>
