<?php

require_once 'vendor/autoload.php';

use App\Exports\AsociadosGrupoFamiliarExport;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    echo "Testing AsociadosGrupoFamiliarExport class...\n";
    
    // Create export instance
    $export = new AsociadosGrupoFamiliarExport();
    echo "Export class instantiated successfully\n";
    
    // Test collection method
    $collection = $export->collection();
    echo "Collection size: " . $collection->count() . "\n";
    
    // Test headings
    $headings = $export->headings();
    echo "Headings: " . implode(', ', $headings) . "\n";
    
    // Test first few rows
    echo "First 5 rows:\n";
    foreach ($collection->take(5) as $index => $row) {
        $mapped = $export->map($row);
        echo "  Row " . ($index + 1) . ": " . implode(' | ', $mapped) . "\n";
    }
    
    echo "Export class test completed successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
