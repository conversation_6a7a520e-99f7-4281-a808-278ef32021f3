<?php

require_once 'vendor/autoload.php';

use App\Models\Asociado;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    echo "Testing Asociado model and relationships...\n";
    
    // Test basic model access
    $count = Asociado::count();
    echo "Total asociados: {$count}\n";
    
    // Test relationships
    $asociado = Asociado::with(['grupoFamiliar.familiares', 'grupoFamiliar.parentesco', 'grupoFamiliar.tipoFamiliar'])
        ->first();
    
    if ($asociado) {
        echo "Testing asociado: {$asociado->nombres} {$asociado->apellidos} (Cédula: {$asociado->cedula_soc})\n";
        echo "Family members: " . $asociado->grupoFamiliar->count() . "\n";
        
        foreach ($asociado->grupoFamiliar as $familiar) {
            echo "  - " . ($familiar->familiares->nombres ?? 'N/A') . " " . ($familiar->familiares->apellidos ?? 'N/A') . 
                 " (" . ($familiar->parentesco->descripcion ?? 'N/A') . ")\n";
        }
    }
    
    echo "Test completed successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
