<div>
    <div class="shadow card card-primary">
        <div class="card-header">
            <h3 class="card-title">Informacion del préstamo</h3>
        </div>

        <div class="card-body">
            <h4 class="text-bold">¡Hola {{ auth()->user()->name }}!, Estás a un paso de obtener tu préstamo. Te
                presentamos
                las
                características y normativas que te ayudarán a entender mejor cómo funciona la linea de préstamo
                seleccionada.
            </h4>


            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Descripción</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($prestamoDetalle['prestamoDescripcion'] as $normativa)
                        <tr>
                            <td><i class="mr-1 fas fa-book"></i> {{ $normativa->descripcion }}</td>
                        </tr>
                    @endforeach

                </tbody>
            </table>

            <br><br><br>
            <p class="text-primary"> <b>*** SI ESTA DE ACUERDO CON LOS TERMINOS Y CONDICIONES, haga click en el
                    boton
                    "Aceptar términos y condiciones" ***</b>
            </p>
        </div>



        <div class="col-12 card-footer d-flex justify-content-end">
            <div class="mr-3 form-check">
                <input wire:model.live='check' class="form-check-input" type="checkbox" value="1"
                    id="defaultCheck">
                <label class="form-check-label" for="defaultCheck">
                    Aceptar términos y condiciones
                </label>
            </div>

            @if ($check == 1)
                <div class="float-right">
                    <x-button class="btn btn-primary justify-content-end" wire:click="$set('confirmar',true)"
                        wire:loading.attr="enable"> <i class="fas fa-check"></i>
                        {{ __('CONTINUAR') }}
                    </x-button>
                </div>
            @endif
            <!-- Fin Formulario datos-personales -->
        </div>
    </div>

    <!-- MODAL CONFIRMACION-->
    <x-confirmation-modal wire:model="confirmar">
        <x-slot name="title">
            {{ __('Aceptar los términos y conficiones') }}
        </x-slot>

        <x-slot name="content">
            <p class="text-justify"> ¿Esta usted de acuerdo con los terminos y condiciones mostradas en la pantalla
                anterior?</p>
        </x-slot>

        <x-slot name="footer">
            <x-secondary-button wire:click="$set('confirmar',false)" wire:loading.attr="disabled">
                {{ __('Cancelar') }}
            </x-secondary-button>
            <x-button wire:click="confirmarTerminos()">
                <i class="fas fa-check"></i> {{ __('ACEPTAR') }}
            </x-button>
        </x-slot>
    </x-confirmation-modal>
    <!-- END MODAL CONFIRMACION -->
</div>
