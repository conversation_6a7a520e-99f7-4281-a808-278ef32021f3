<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class ServicioFunerario
 * 
 * @property int $idservifu
 * @property int $idcontrato
 * @property int $idasociado
 * @property int $periodo_cont
 * @property string $cedula_soc
 * @property Carbon $fecha_cont
 * @property float $tasa_decambio
 * @property string $codigo_deduc
 * @property int $metodo_depago
 * @property int|null $carga_base
 * @property int|null $carga_adic
 * @property float $monto_cont
 * @property float|null $monto_cargabase
 * @property float|null $monto_cargaadic
 * @property float $monto_total
 * @property float|null $monto_pagado
 * @property float|null $monto_pendiente
 * @property int|null $numero_cn
 * @property float|null $montoc_normal
 * @property int|null $numero_cesp
 * @property float|null $monto_cesp
 * @property float $saldo_cesp
 * @property bool $condic_pagocn
 * @property bool $condic_pagoce
 * @property bool $status_depago
 * @property string|null $observaciones
 * @property Carbon|null $fecha_anulacion
 * @property float $saldo_cnormales
 * @property float $cuota_quinc
 * @property int $status_cont
 * @property float|null $debitos
 * @property float|null $creditos
 *
 * @package App\Models
 */
class ServicioFunerario extends Model
{
	protected $table = 'servicio_funerario';
	protected $primaryKey = 'idservifu';
	public $timestamps = false;

	protected $casts = [
		'idcontrato' => 'int',
		'idasociado' => 'int',
		'periodo_cont' => 'int',
		'fecha_cont' => 'datetime',
		'tasa_decambio' => 'float',
		'metodo_depago' => 'int',
		'carga_base' => 'int',
		'carga_adic' => 'int',
		'monto_cont' => 'float',
		'monto_cargabase' => 'float',
		'monto_cargaadic' => 'float',
		'monto_total' => 'float',
		'monto_pagado' => 'float',
		'monto_pendiente' => 'float',
		'numero_cn' => 'int',
		'montoc_normal' => 'float',
		'numero_cesp' => 'int',
		'monto_cesp' => 'float',
		'saldo_cesp' => 'float',
		'condic_pagocn' => 'bool',
		'condic_pagoce' => 'bool',
		'status_depago' => 'bool',
		'fecha_anulacion' => 'datetime',
		'saldo_cnormales' => 'float',
		'cuota_quinc' => 'float',
		'status_cont' => 'int',
		'debitos' => 'float',
		'creditos' => 'float'
	];

	protected $fillable = [
		'idcontrato',
		'idasociado',
		'periodo_cont',
		'cedula_soc',
		'fecha_cont',
		'tasa_decambio',
		'codigo_deduc',
		'metodo_depago',
		'carga_base',
		'carga_adic',
		'monto_cont',
		'monto_cargabase',
		'monto_cargaadic',
		'monto_total',
		'monto_pagado',
		'monto_pendiente',
		'numero_cn',
		'montoc_normal',
		'numero_cesp',
		'monto_cesp',
		'saldo_cesp',
		'condic_pagocn',
		'condic_pagoce',
		'status_depago',
		'observaciones',
		'fecha_anulacion',
		'saldo_cnormales',
		'cuota_quinc',
		'status_cont',
		'debitos',
		'creditos'
	];
}
