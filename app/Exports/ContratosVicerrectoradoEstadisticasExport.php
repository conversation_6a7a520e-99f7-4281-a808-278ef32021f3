<?php

namespace App\Exports;

use App\Models\Contrato;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;

class ContratosVicerrectoradoEstadisticasExport implements 
    FromCollection, 
    WithHeadings, 
    WithMapping, 
    WithStyles, 
    ShouldAutoSize,
    WithEvents
{
    protected $selectedRows;
    protected $filters;
    protected $data;
    protected $totalContratos = 0;
    protected $totalActivos = 0;
    protected $totalAnulados = 0;
    protected $totalSuspendidos = 0;
    protected $totalCargaBasica = 0;
    protected $totalCargaAdicional = 0;

    public function __construct($selectedRows = null, $filters = null)
    {
        $this->selectedRows = $selectedRows;
        $this->filters = $filters;
        $this->prepareData();
    }

    /**
     * Prepara los datos estadísticos agrupando contratos por vicerrectorado
     */
    private function prepareData()
    {
        $query = Contrato::query()
            ->join('config_tools', 'servicio_funerario_contratos.estatus', '=', 'config_tools.codigo')
            ->where('config_tools.modulo', 'servicio-funerario.contratos')
            ->where('config_tools.referencia', 'estatus')
            ->join('asociados as a', 'servicio_funerario_contratos.asociado_idasociado', '=', 'a.idasociado')
            ->join('codigos_varios as v', 'a.codigo_dep', '=', 'v.codigo_v')
            ->where('v.ref_v', 'D');

        // Aplicar filtros si existen
        if ($this->filters) {
            if (!empty($this->filters['estatus'])) {
                $query->where('servicio_funerario_contratos.estatus', $this->filters['estatus']);
            }
            if (!empty($this->filters['periodo'])) {
                $query->where('servicio_funerario_contratos.periodo', $this->filters['periodo']);
            }
            if (!empty($this->filters['fecha_desde'])) {
                $query->whereDate('servicio_funerario_contratos.created_at', '>=', $this->filters['fecha_desde']);
            }
            if (!empty($this->filters['fecha_hasta'])) {
                $query->whereDate('servicio_funerario_contratos.created_at', '<=', $this->filters['fecha_hasta']);
            }
        }

        // Si hay filas seleccionadas, filtrar solo esas
        if ($this->selectedRows && count($this->selectedRows) > 0) {
            $query->whereIn('servicio_funerario_contratos.id', $this->selectedRows);
        }

        // Agrupar por vicerrectorado y contar contratos con detalles
        $estadisticas = $query
            ->select(
                'v.descri_v as vicerrectorado',
                'v.codigo_v as codigo_vicerrectorado',
                DB::raw('COUNT(servicio_funerario_contratos.id) as total_contratos'),
                DB::raw('SUM(CASE WHEN servicio_funerario_contratos.estatus = 1 THEN 1 ELSE 0 END) as contratos_activos'),
                DB::raw('SUM(CASE WHEN servicio_funerario_contratos.estatus = 2 THEN 1 ELSE 0 END) as contratos_anulados'),
                DB::raw('SUM(CASE WHEN servicio_funerario_contratos.estatus = 3 THEN 1 ELSE 0 END) as contratos_suspendidos'),
                DB::raw('SUM(servicio_funerario_contratos.numero_cb) as total_carga_basica'),
                DB::raw('SUM(servicio_funerario_contratos.numero_ca) as total_carga_adicional')
            )
            ->groupBy('v.codigo_v', 'v.descri_v')
            ->orderBy('v.descri_v', 'asc')
            ->get();

        $this->data = collect();

        foreach ($estadisticas as $estadistica) {
            $this->data->push([
                'tipo' => 'vicerrectorado',
                'vicerrectorado' => $estadistica->vicerrectorado,
                'total_contratos' => $estadistica->total_contratos,
                'contratos_activos' => $estadistica->contratos_activos,
                'contratos_anulados' => $estadistica->contratos_anulados,
                'contratos_suspendidos' => $estadistica->contratos_suspendidos,
                'carga_basica' => $estadistica->total_carga_basica,
                'carga_adicional' => $estadistica->total_carga_adicional,
                'porcentaje' => 0 // Se calculará después
            ]);
            $this->totalContratos += $estadistica->total_contratos;
            $this->totalActivos += $estadistica->contratos_activos;
            $this->totalAnulados += $estadistica->contratos_anulados;
            $this->totalSuspendidos += $estadistica->contratos_suspendidos;
            $this->totalCargaBasica += $estadistica->total_carga_basica;
            $this->totalCargaAdicional += $estadistica->total_carga_adicional;
        }

        // Calcular porcentajes
        $this->data = $this->data->map(function ($item) {
            if ($this->totalContratos > 0) {
                $item['porcentaje'] = round(($item['total_contratos'] / $this->totalContratos) * 100, 2);
            }
            return $item;
        });

        // Agregar fila de total
        $this->data->push([
            'tipo' => 'total',
            'vicerrectorado' => 'TOTAL GENERAL',
            'total_contratos' => $this->totalContratos,
            'contratos_activos' => $this->totalActivos,
            'contratos_anulados' => $this->totalAnulados,
            'contratos_suspendidos' => $this->totalSuspendidos,
            'carga_basica' => $this->totalCargaBasica,
            'carga_adicional' => $this->totalCargaAdicional,
            'porcentaje' => 100.00
        ]);
    }

    /**
     * @return Collection
     */
    public function collection()
    {
        return $this->data;
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'VICERRECTORADO',
            'TOTAL CONTRATOS',
            'ACTIVOS',
            'ANULADOS',
            'SUSPENDIDOS',
            'CARGA BÁSICA',
            'CARGA ADICIONAL',
            'PORCENTAJE (%)'
        ];
    }

    /**
     * @param mixed $row
     * @return array
     */
    public function map($row): array
    {
        return [
            $row['vicerrectorado'],
            $row['total_contratos'],
            $row['contratos_activos'],
            $row['contratos_anulados'],
            $row['contratos_suspendidos'],
            $row['carga_basica'],
            $row['carga_adicional'],
            $row['porcentaje'] . '%'
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Estilo para el encabezado
            1 => [
                'font' => ['bold' => true, 'size' => 12],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['argb' => '4472C4']
                ],
                'font' => ['color' => ['argb' => 'FFFFFF'], 'bold' => true, 'size' => 12],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical' => Alignment::VERTICAL_CENTER
                ]
            ]
        ];
    }

    /**
     * @return array
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $sheet = $event->sheet->getDelegate();
                $rowIndex = 2; // Comenzar después del encabezado
                
                foreach ($this->data as $row) {
                    if ($row['tipo'] === 'total') {
                        // Estilo especial para la fila de total
                        $sheet->getStyle("A{$rowIndex}:H{$rowIndex}")->applyFromArray([
                            'font' => [
                                'bold' => true,
                                'size' => 12,
                                'color' => ['argb' => 'FFFFFF']
                            ],
                            'fill' => [
                                'fillType' => Fill::FILL_SOLID,
                                'startColor' => ['argb' => '70AD47']
                            ],
                            'borders' => [
                                'allBorders' => [
                                    'borderStyle' => Border::BORDER_THICK,
                                    'color' => ['argb' => '000000']
                                ]
                            ],
                            'alignment' => [
                                'horizontal' => Alignment::HORIZONTAL_CENTER,
                                'vertical' => Alignment::VERTICAL_CENTER
                            ]
                        ]);
                    } else {
                        // Estilo para filas de vicerrectorados
                        $sheet->getStyle("A{$rowIndex}:H{$rowIndex}")->applyFromArray([
                            'font' => [
                                'size' => 11
                            ],
                            'fill' => [
                                'fillType' => Fill::FILL_SOLID,
                                'startColor' => ['argb' => 'F2F2F2']
                            ],
                            'borders' => [
                                'allBorders' => [
                                    'borderStyle' => Border::BORDER_THIN,
                                    'color' => ['argb' => '000000']
                                ]
                            ],
                            'alignment' => [
                                'horizontal' => Alignment::HORIZONTAL_CENTER,
                                'vertical' => Alignment::VERTICAL_CENTER
                            ]
                        ]);
                    }
                    $rowIndex++;
                }

                // Ajustar el ancho de las columnas
                $sheet->getColumnDimension('A')->setWidth(40);
                $sheet->getColumnDimension('B')->setWidth(15);
                $sheet->getColumnDimension('C')->setWidth(12);
                $sheet->getColumnDimension('D')->setWidth(12);
                $sheet->getColumnDimension('E')->setWidth(15);
                $sheet->getColumnDimension('F')->setWidth(15);
                $sheet->getColumnDimension('G')->setWidth(18);
                $sheet->getColumnDimension('H')->setWidth(15);

                // Aplicar bordes al encabezado
                $sheet->getStyle('A1:H1')->applyFromArray([
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => Border::BORDER_THICK,
                            'color' => ['argb' => '000000']
                        ]
                    ]
                ]);

                // Agregar título del reporte
                $sheet->insertNewRowBefore(1, 2);
                $sheet->setCellValue('A1', 'REPORTE ESTADÍSTICO DE CONTRATOS POR VICERRECTORADO');
                $sheet->mergeCells('A1:H1');
                $sheet->getStyle('A1')->applyFromArray([
                    'font' => [
                        'bold' => true,
                        'size' => 14,
                        'color' => ['argb' => 'FFFFFF']
                    ],
                    'fill' => [
                        'fillType' => Fill::FILL_SOLID,
                        'startColor' => ['argb' => '2F5597']
                    ],
                    'alignment' => [
                        'horizontal' => Alignment::HORIZONTAL_CENTER,
                        'vertical' => Alignment::VERTICAL_CENTER
                    ]
                ]);

                // Agregar fecha de generación
                $sheet->setCellValue('A2', 'Fecha de generación: ' . date('d/m/Y H:i:s'));
                $sheet->mergeCells('A2:H2');
                $sheet->getStyle('A2')->applyFromArray([
                    'font' => [
                        'italic' => true,
                        'size' => 10
                    ],
                    'alignment' => [
                        'horizontal' => Alignment::HORIZONTAL_CENTER
                    ]
                ]);
            }
        ];
    }
}
