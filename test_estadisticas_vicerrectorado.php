<?php

require_once 'vendor/autoload.php';

use App\Exports\ContratosVicerrectoradoEstadisticasExport;
use Maatwebsite\Excel\Facades\Excel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    echo "Testing ContratosVicerrectoradoEstadisticasExport class...\n";
    
    // Create export instance
    $export = new ContratosVicerrectoradoEstadisticasExport();
    echo "Export class instantiated successfully\n";
    
    // Test collection method
    $collection = $export->collection();
    echo "Collection size: " . $collection->count() . "\n";
    
    // Test headings
    $headings = $export->headings();
    echo "Headings: " . implode(', ', $headings) . "\n";
    
    // Show statistics
    echo "\nEstadísticas por Vicerrectorado:\n";
    echo str_repeat("-", 80) . "\n";
    printf("%-50s %-15s %-15s\n", "VICERRECTORADO", "CONTRATOS", "PORCENTAJE");
    echo str_repeat("-", 80) . "\n";
    
    foreach ($collection as $row) {
        $mapped = $export->map($row);
        printf("%-50s %-15s %-15s\n", $mapped[0], $mapped[1], $mapped[2]);
    }
    
    echo str_repeat("-", 80) . "\n";
    
    // Test Excel generation
    echo "\nTesting Excel generation...\n";
    $filename = 'test_estadisticas_vicerrectorado_' . date('Y-m-d_H-i-s') . '.xlsx';
    $filepath = storage_path('app/' . $filename);
    
    Excel::store($export, $filename);
    
    if (file_exists($filepath)) {
        $filesize = filesize($filepath);
        echo "Excel file generated successfully!\n";
        echo "File: {$filepath}\n";
        echo "Size: " . number_format($filesize) . " bytes\n";
        
        // Clean up test file
        unlink($filepath);
        echo "Test file cleaned up.\n";
    } else {
        echo "Error: Excel file was not created.\n";
    }
    
    echo "\nTest completed successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
