<?php

namespace Modules\Backend\Livewire\GestionDePago\Recaudacion\Components\Form;

use Livewire\Component;
use App\Models\Asociado;
use App\Models\DetalleDenomina;

#[Lazy]
class FormDetallePago extends Component
{
    public  $conceptos;
    public  Asociado $asociado;
    public $fecha_inicio;
    public $fecha_fin;
    public $codigo_trans = [];
    public $pagos;
    public $selectedConceptos;

    protected $rules = [
        'fecha_inicio' => 'required',
        'fecha_fin' => 'required',
        'codigo_trans' => 'required',
    ];

    protected $messages = [
        'fecha_inicio.required' => 'La fecha de inicio es requerida.',
        'fecha_fin.required' => 'La fecha de fin es requerida.',
        'codigo_trans.required' => 'El concepto es requerido.',
    ];
    public function mount()
    {
        $this->conceptos = $this->getConceptos();
    }

    public function render()
    {
        return view('backend::livewire.gestion-de-pago.recaudacion.components.form.form-detalle-pago');
    }

    public function getPagos()
    {
        //todos los conceptos
        $this->conceptos = $this->getConceptos();
        //conceptos selecionados
        $this->selectedConceptos = $this->getConceptosSeleccionados();


        $this->validate();
        $query = DetalleDenomina::query()
            ->where('detalle_denominas.cedula_soc', $this->asociado->cedula_soc);

        if (!empty($this->codigo_trans)) {
            $query = $query->whereIn('detalle_denominas.codigo_deduc', $this->codigo_trans);
        }

        if ($this->fecha_inicio && $this->fecha_fin) {
            $query = $query->whereBetween('detalle_denominas.fecha_denomina', [$this->fecha_inicio, $this->fecha_fin]);
        }

        $this->pagos = $query->with('bancosDetalleDomicPagos', 'controlDeNomina')->get();
    }


    /**
     * Get the conceptos.
     *
     * This method retrieves the conceptos from the DetalleDenomina table
     * based on the cedula_soc of the asociado. It excludes conceptos
     * that have aplica_apopatr set to 0. The conceptos are grouped by
     * codigo_deduc and return the codigo_trans and descripcion.
     *
     * @return \Illuminate\Support\Collection
     */
    public function getConceptos()
    {
        return DetalleDenomina::select('codigos_transacciones.codigo_trans', 'codigos_transacciones.descripcion')
            ->join('codigos_transacciones', 'codigos_transacciones.codigo_trans', '=', 'detalle_denominas.codigo_deduc')
            ->where('detalle_denominas.cedula_soc', $this->asociado->cedula_soc)
            ->where('aplica_apopatr', 0)
            ->groupBy('detalle_denominas.codigo_deduc', 'codigos_transacciones.codigo_trans', 'codigos_transacciones.descripcion')
            ->get();
    }

    public function getConceptosSeleccionados()
    {
        return DetalleDenomina::select('codigos_transacciones.codigo_trans', 'codigos_transacciones.descripcion')
            ->join('codigos_transacciones', 'codigos_transacciones.codigo_trans', '=', 'detalle_denominas.codigo_deduc')
            ->where('detalle_denominas.cedula_soc', $this->asociado->cedula_soc)
            ->where('aplica_apopatr', 0)
            ->whereIn('detalle_denominas.codigo_deduc', $this->codigo_trans)
            ->groupBy('detalle_denominas.codigo_deduc', 'codigos_transacciones.codigo_trans', 'codigos_transacciones.descripcion')
            ->get();
    }
}
