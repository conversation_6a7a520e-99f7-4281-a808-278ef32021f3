<?php

namespace Modules\Backend\Livewire\Encuestas\Pages;

use Livewire\Component;
use App\Models\Contrato;
use App\Models\Encuesta;

class Index extends Component
{
    const PERMISSIONS = [
        'index' => 'admin.encuestas.index',
    ];

    public function render()
    {
        return view('backend::livewire.encuestas.pages.index')->with([
            'total_encuestas' => Encuesta::all()->count(),
            'total_encuestas_activos' => Encuesta::where('estatus', '1')->count(),
            'total_encuestas_inactivas' => Encuesta::where('estatus', '2')->count(),
        ]);
    }
}
