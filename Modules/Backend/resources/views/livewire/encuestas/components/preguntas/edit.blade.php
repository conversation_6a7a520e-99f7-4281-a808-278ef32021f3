   <div>
       <x-dialog-modal wire:model="modalEdit" maxWidth="xl">
           <x-slot name="title">
               {{ __('Editar pregunta') }}
           </x-slot>
           <x-slot name="content">
               <div class="form-row">
                   <div class="col-md-12">
                       <div class="form-row ">
                           <div class="col-md-12">
                               <div class="form-group">
                                   <x-label for="pregunta" value="{{ __('Pregunta') }}" />
                                   <x-adminlte-textarea wire:model="pregunta" name="taMsg" rows=5 igroup-size="sm"
                                       label-class="text-primary" placeholder="Escriba su pregunta..." disable-feedback>
                                       <x-slot name="prependSlot">
                                           <div class="input-group-text">
                                               <i class="fas fa-lg fa-comment-dots text-primary"></i>
                                           </div>
                                       </x-slot>

                                   </x-adminlte-textarea>
                                   <x-input-error for="pregunta" />
                               </div>
                           </div>

                           <div class="col-md-12">
                               <div class="form-group">
                                   <x-label for="tipo" value="{{ __('Tipo de respuesta') }}" />
                                   <div class="input-group">
                                       <div class="input-group-prepend d-flex d-sm-none d-xl-flex">
                                           <span class="input-group-text">
                                               <i class="fas fa-question fa-fw"></i>
                                           </span>
                                       </div>
                                       <select wire:model.live="tipo" name="tipo" id="tipo"
                                           class="form-control">
                                           <option value="">---- Seleccione ----</option>
                                           <option value="radio">Respuesta Sencila/Botón Círculo</option>
                                           <option value="checkbox">Respuesta Multiple/Cuadros de Chequeo
                                           </option>
                                           <option value="number">Campo Numérico</option>
                                           <option value="textarea">Campo de Texto/ Área de Texto</option>
                                       </select>

                                   </div>
                                   <x-input-error for="tipo" />
                               </div>
                           </div>

                           <div class="col-md-12">
                               @if ($tipo)
                                   <div class="border">
                                       <div class="card-body">
                                           <div class="form-group">
                                               @if ($tipo == 'radio' || $tipo == 'checkbox')
                                                   <div class="alert alert-warning col">
                                                       ¡Atención! Si desea agregar más opciones,
                                                       asegúrese de presionar el botón correspondiente.
                                                   </div>
                                                   <div class="row">
                                                       @foreach ($inputs as $key => $value)
                                                           <div class="col-2">
                                                               <x-input type="{{ $tipo }}"
                                                                   id="opciones.{{ $key }}" name="opciones" />
                                                           </div>

                                                           <div class="col-8">
                                                               <input type="text" class="form-control"
                                                                   wire:model="inputs.{{ $key }}"
                                                                   placeholder="{{ __('Opcion') }} {{ $key + 1 }}" />
                                                           </div>

                                                           <div class="col-2">
                                                               <x-button wire:click="removeInput({{ $key }})"
                                                                   class="btn btn-danger btn-sm" title="Eliminar">
                                                                   <i class="fas fa-trash fa-fw"></i>
                                                               </x-button>
                                                           </div>

                                                           <div class="text-center col-12">
                                                               <x-input-error for="inputs.{{ $key }}" />
                                                           </div>
                                                       @endforeach
                                                   </div>
                                               @elseif ($tipo == 'number')
                                                   <div class="alert alert-warning col">
                                                       ¡Atención! Si desea agregar más opciones,
                                                       asegúrese de presionar el botón correspondiente.
                                                   </div>
                                                   <div class="row">
                                                       @foreach ($inputs as $key => $value)
                                                           <div class="col-2">
                                                               <x-input type="{{ $tipo }}" disabled
                                                                   id="opciones.{{ $key }}" name="opciones" />
                                                           </div>

                                                           <div class="col-8">
                                                               <input type="text" class="form-control"
                                                                   wire:model="inputs.{{ $key }}"
                                                                   placeholder="{{ __('Opcion') }} {{ $key + 1 }}" />
                                                           </div>

                                                           <div class="col-2">
                                                               <x-button wire:click="removeInput({{ $key }})"
                                                                   class="btn btn-danger btn-sm" title="Eliminar">
                                                                   <i class="fas fa-trash fa-fw"></i>
                                                               </x-button>
                                                           </div>

                                                           <div class="text-center col-12">
                                                               <x-input-error for="inputs.{{ $key }}" />
                                                           </div>
                                                       @endforeach
                                                   </div>
                                               @elseif ($tipo == 'textarea')
                                                   <div class="row">
                                                       <div class="col-12">
                                                           <x-label for="opciones" value="{{ __('Texto') }}" />
                                                       </div>
                                                       <div class="col-12">
                                                           <div class="input-group">
                                                               <div
                                                                   class="input-group-prepend d-flex d-sm-none d-xl-flex">
                                                                   <span class="input-group-text">
                                                                       <i class="fas fa-list fa-fw"></i>
                                                                   </span>
                                                               </div>
                                                               <textarea wire:model="opciones" id="opciones" class="form-control" type="textarea" name="opciones" required autofocus
                                                                   disabled></textarea>
                                                           </div>
                                                       </div>
                                                   </div>
                                               @endif
                                           </div>
                                       </div>
                                       <div class="card-footer">
                                           @if ($tipo == 'radio' || $tipo == 'checkbox' || $tipo == 'number')
                                               <x-button wire:click="addInput" class="float-right btn btn-success">
                                                   <i class="fas fa-plus fa-fw"></i> Agregar
                                                   Opción
                                               </x-button>
                                           @endif
                                       </div>
                                   </div>
                               @endif
                           </div>
                       </div>
                   </div>
               </div>
           </x-slot>
           <x-slot name="footer">
               <x-secondary-button wire:click="$set('modalEdit', false)" wire:loading.attr="disabled">
                   {{ __('Close') }}
               </x-secondary-button>
               <x-button wire:click="store()" wire:loading.attr="disabled">
                   <i class="fas fa-save fa-fw" aria-hidden="true"></i> {{ __('Save') }}
               </x-button>
           </x-slot>
       </x-dialog-modal>
   </div>
