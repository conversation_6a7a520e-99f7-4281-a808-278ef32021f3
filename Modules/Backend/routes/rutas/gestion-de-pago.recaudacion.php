<?php

use Illuminate\Support\Facades\Route;
use Modules\Backend\Livewire\GestionDePago\Recaudacion\Pages\Index;
use Modules\Backend\Livewire\GestionDePago\Recaudacion\Pages\Show;
use Modules\Backend\Livewire\GestionDePago\Recaudacion\Pages\Create;
use Modules\Backend\Livewire\GestionDePago\Recaudacion\Pages\Edit;
use Modules\Backend\Livewire\GestionDePago\Recaudacion\Pages\Destroy;
use Modules\Backend\Livewire\GestionDePago\Recaudacion\Pages\Update;



/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
 */

Route::prefix('admin/gestion-de-pago/recaudacion')->group(
    function () {
        Route::get('/', Index::class)->name('admin.gestion-de-pago.recaudacion.index');
        Route::get('/show/{asociado}', Show::class)->name('admin.gestion-de-pago.recaudacion.show');
        Route::get('/create', Create::class)->name('admin.gestion-de-pago.recaudacion.create');
        Route::get('/edit/{asociado}', Edit::class)->name('admin.gestion-de-pago.recaudacion.edit');
        Route::get('/update/{asociado}', Update::class)->name('admin.gestion-de-pago.recaudacion.update');
        Route::get('/delete/{asociado}', Destroy::class)->name('admin.gestion-de-pago.recaudacion.destroy');
    }
);
