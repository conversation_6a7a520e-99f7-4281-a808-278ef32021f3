<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use App\Models\BancosDetalleDomicpago;
use Illuminate\Database\Eloquent\Model;

/**
 * Class PrestamosRelacionDecuota
 *
 * @property int $idcuota
 * @property string $cedula_soc
 * @property string $codigo_prest
 * @property int $numero_prest
 * @property bool $tipo_cuota
 * @property Carbon $fecha_pago
 * @property string $concepto
 * @property float $monto_cuota
 * @property float $tasa_cambio
 * @property float $monto_cuotai
 * @property float $monto_pago
 * @property float $tasa_depago
 * @property float $monto_pagoi
 * @property float $debitos
 * @property float $creditos
 * @property float $capital_amo
 * @property float $interes_amo
 * @property float $saldo_prest
 * @property float $saldo_cesp
 * @property float $tasa_i
 * @property bool $origen_delpago
 * @property bool $nc_tran
 * @property bool $tipo_trans
 * @property string $reftransac
 * @property string $codigo_us
 * @property string $hora
 * @property float $difer_index
 * @property int|null $idcesp
 * @property float $debitos_index
 * @property float $creditos_index
 * @property float $capital_index
 * @property float $interes_index
 * @property int $idmovnomi
 * @property float $saldoprest_index
 * @property bool|null $statusdif
 * @property float $difer_cambio
 * @property int $idtransban
 * @property string $codigo_banco
 *
 * @package App\Models
 */
class PrestamosRelacionDecuota extends Model
{
    protected $table = 'prestamos_relacion_decuotas';
    protected $primaryKey = 'idcuota';
    public $timestamps = false;

    protected $casts = [
        'numero_prest' => 'int',
        'tipo_cuota' => 'bool',
        'fecha_pago' => 'datetime',
        'monto_cuota' => 'float',
        'tasa_cambio' => 'float',
        'monto_cuotai' => 'float',
        'monto_pago' => 'float',
        'tasa_depago' => 'float',
        'monto_pagoi' => 'float',
        'debitos' => 'float',
        'creditos' => 'float',
        'capital_amo' => 'float',
        'interes_amo' => 'float',
        'saldo_prest' => 'float',
        'saldo_cesp' => 'float',
        'tasa_i' => 'float',
        'origen_delpago' => 'bool',
        'nc_tran' => 'bool',
        'tipo_trans' => 'bool',
        'difer_index' => 'float',
        'idcesp' => 'int',
        'debitos_index' => 'float',
        'creditos_index' => 'float',
        'capital_index' => 'float',
        'interes_index' => 'float',
        'idmovnomi' => 'int',
        'saldoprest_index' => 'float',
        'statusdif' => 'bool',
        'difer_cambio' => 'float',
        'idtransban' => 'int'
    ];

    protected $fillable = [
        'cedula_soc',
        'codigo_prest',
        'numero_prest',
        'tipo_cuota',
        'fecha_pago',
        'concepto',
        'monto_cuota',
        'tasa_cambio',
        'monto_cuotai',
        'monto_pago',
        'tasa_depago',
        'monto_pagoi',
        'debitos',
        'creditos',
        'capital_amo',
        'interes_amo',
        'saldo_prest',
        'saldo_cesp',
        'tasa_i',
        'origen_delpago',
        'nc_tran',
        'tipo_trans',
        'reftransac',
        'codigo_us',
        'hora',
        'difer_index',
        'idcesp',
        'debitos_index',
        'creditos_index',
        'capital_index',
        'interes_index',
        'idmovnomi',
        'saldoprest_index',
        'statusdif',
        'difer_cambio',
        'idtransban',
        'codigo_banco'
    ];


    public function bancosDetalleDomicpago()
    {
        return $this->hasMany(BancosDetalleDomicpago::class, 'idmovnomi', 'idmovnomi');
    }
}
