<?php

use Illuminate\Support\Facades\Route;
use Mo<PERSON>les\Backend\Livewire\Configuration\Prestamos\Pages\Index;
use Modules\Backend\Livewire\Configuration\Prestamos\Pages\Show;
use Modules\Backend\Livewire\Configuration\Prestamos\Pages\Create;
use Modules\Backend\Livewire\Configuration\Prestamos\Pages\Edit;
use Mo<PERSON>les\Backend\Livewire\Configuration\Prestamos\Pages\Destroy;


/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
 */

Route::prefix('admin/configuration/prestamos')->group(
    function () {
        Route::get('/', Index::class)->name('admin.configuration.prestamos.index');
        Route::get('/show', Show::class)->name('admin.configuration.prestamos.show');
        Route::get('/create', Create::class)->name('admin.configuration.prestamos.create');
        Route::get('/edit/{prestamo}', Edit::class)->name('admin.configuration.prestamos.edit');
        Route::get('/delete/{prestamo}', Destroy::class)->name('admin.configuration.delete');
    }
);
