<?php

namespace App\Exports;

use App\Models\Asociado;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

class AsociadosGrupoFamiliarExport implements 
    FromCollection, 
    WithHeadings, 
    WithMapping, 
    WithStyles, 
    ShouldAutoSize,
    WithEvents
{
    protected $data;
    protected $rowIndex = 1; // Para rastrear el índice de fila actual

    public function __construct()
    {
        $this->prepareData();
    }

    /**
     * Prepara los datos organizando asociados con sus grupos familiares
     */
    private function prepareData()
    {
        $this->data = collect();
        
        // Obtener asociados ordenados por cédula
        $asociados = Asociado::with(['grupoFamiliar.familiares', 'grupoFamiliar.parentesco', 'grupoFamiliar.tipoFamiliar'])
            ->orderBy('cedula_soc', 'asc')
            ->get();

        foreach ($asociados as $asociado) {
            // Agregar el asociado principal
            $this->data->push([
                'tipo' => 'asociado',
                'cedula' => $asociado->cedula_soc,
                'nombres' => $asociado->nombres,
                'apellidos' => $asociado->apellidos,
                'parentesco' => 'TITULAR',
                'tipo_familiar' => 'ASOCIADO',
                'fecha_nacimiento' => $asociado->fecha_nac ? date('d/m/Y', strtotime($asociado->fecha_nac)) : 'N/A'
            ]);

            // Agregar los familiares
            foreach ($asociado->grupoFamiliar as $familiar) {
                $this->data->push([
                    'tipo' => 'familiar',
                    'cedula' => $familiar->familiares->cedula ?? 'N/A',
                    'nombres' => $familiar->familiares->nombres ?? 'N/A',
                    'apellidos' => $familiar->familiares->apellidos ?? 'N/A',
                    'parentesco' => $familiar->parentesco->descripcion ?? 'N/A',
                    'tipo_familiar' => $familiar->tipoFamiliar->descripcion ?? 'N/A',
                    'fecha_nacimiento' => $familiar->familiares->fecha_nac ? 
                        date('d/m/Y', strtotime($familiar->familiares->fecha_nac)) : 'N/A'
                ]);
            }
        }
    }

    /**
     * @return Collection
     */
    public function collection()
    {
        return $this->data;
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'CÉDULA',
            'NOMBRES',
            'APELLIDOS',
            'PARENTESCO',
            'TIPO FAMILIAR',
            'FECHA NACIMIENTO'
        ];
    }

    /**
     * @param mixed $row
     * @return array
     */
    public function map($row): array
    {
        $this->rowIndex++;
        
        return [
            $row['cedula'],
            $row['nombres'],
            $row['apellidos'],
            $row['parentesco'],
            $row['tipo_familiar'],
            $row['fecha_nacimiento']
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Estilo para el encabezado
            1 => [
                'font' => ['bold' => true, 'size' => 12],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['argb' => 'DDDDDD']
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical' => Alignment::VERTICAL_CENTER
                ]
            ]
        ];
    }

    /**
     * @return array
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $sheet = $event->sheet->getDelegate();
                $rowIndex = 2; // Comenzar después del encabezado
                
                foreach ($this->data as $row) {
                    if ($row['tipo'] === 'asociado') {
                        // Estilo para asociados (filas principales)
                        $sheet->getStyle("A{$rowIndex}:F{$rowIndex}")->applyFromArray([
                            'font' => [
                                'bold' => true,
                                'size' => 11
                            ],
                            'fill' => [
                                'fillType' => Fill::FILL_SOLID,
                                'startColor' => ['argb' => 'F0F8FF']
                            ],
                            'borders' => [
                                'allBorders' => [
                                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                                    'color' => ['argb' => '000000']
                                ]
                            ]
                        ]);
                    } else {
                        // Estilo para familiares (filas secundarias con indentación)
                        $sheet->getStyle("A{$rowIndex}:F{$rowIndex}")->applyFromArray([
                            'font' => [
                                'size' => 10
                            ],
                            'fill' => [
                                'fillType' => Fill::FILL_SOLID,
                                'startColor' => ['argb' => 'FAFAFA']
                            ],
                            'borders' => [
                                'allBorders' => [
                                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                                    'color' => ['argb' => 'CCCCCC']
                                ]
                            ]
                        ]);
                        
                        // Aplicar indentación a las celdas de familiares
                        $sheet->getStyle("A{$rowIndex}:C{$rowIndex}")->getAlignment()->setIndent(2);
                    }
                    $rowIndex++;
                }

                // Ajustar el ancho de las columnas
                $sheet->getColumnDimension('A')->setWidth(15);
                $sheet->getColumnDimension('B')->setWidth(25);
                $sheet->getColumnDimension('C')->setWidth(25);
                $sheet->getColumnDimension('D')->setWidth(20);
                $sheet->getColumnDimension('E')->setWidth(15);
                $sheet->getColumnDimension('F')->setWidth(18);

                // Aplicar bordes al encabezado
                $sheet->getStyle('A1:F1')->applyFromArray([
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THICK,
                            'color' => ['argb' => '000000']
                        ]
                    ]
                ]);
            }
        ];
    }
}
