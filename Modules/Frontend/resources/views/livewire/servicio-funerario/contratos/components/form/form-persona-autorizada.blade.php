<div>
    <div class="row">

        <div class="container">

            <div class="mb-1 d-flex justify-content-end">
                <x-button wire:click="$set('modalCreatePersonaAutorizada',true)" class="btn btn-primary" title="Nuevo">
                    <i class="fas fa-plus fa-fw"></i> Agregar persona de contacto
                </x-button>
            </div>
        </div>
    </div>

    <x-dialog-modal wire:model="modalCreatePersonaAutorizada" maxWidth="xl">
        <form wire:submit.prevent="save">
            <x-slot name="title">
                {{ __('Agregar persona autorizada') }}
            </x-slot>
            <x-slot name="content">
                <div class="container">
                    @if (session()->has('message'))
                        <div class="alert alert-success"><i class="fas fa-info-circle"></i> {{ session('message') }}
                        </div>
                    @endif
                    <div class="form-row">
                        <legend class="mt-2 mb-0 text-primary">Información de basica</legend>
                        <div class="col-md-12">
                            <div class="form-row ">
                                <div class="col-md-4">

                                    <div class="form-group">
                                        <x-label for="cedula" value="{{ __('Cédula') }}" />
                                        <x-input id="cedula" class="form-control" type="number" name="cedula"
                                            wire:model="cedula" />
                                        <x-input-error for="cedula" class="mt-2" />
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="form-group">
                                        <x-label for="nombres" value="{{ __('Nombres') }}" />
                                        <x-input id="nombres" class="form-control" type="text" name="nombres"
                                            wire:model="nombres" />
                                        <x-input-error for="nombres" class="mt-2" />
                                        <small class="form-text text-muted">Nombres del familiar</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <x-label for="apellidos" value="{{ __('Apellidos') }}" />
                                        <x-input id="apellidos" class="form-control" type="text" name="apellidos"
                                            wire:model="apellidos" />
                                        <x-input-error for="apellidos" class="mt-2" />
                                        <small class="form-text text-muted">Apellidos del familiar</small>

                                    </div>
                                </div>
                                <hr>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <x-label for="parentescos_id" value="{{ __('Parentesco') }}" />
                                        <select class="form-control" name="parentescos_id" wire:model="parentescos_id">
                                            <option>--- Seleccione ---</option>
                                            @foreach ($parentescos as $parentesco)
                                                <option value="{{ $parentesco->id }}">{{ $parentesco->descripcion }}
                                                </option>
                                            @endforeach
                                        </select>
                                        <x-input-error for="parentescos_id" />
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <x-label value="Genero" />
                                    <select class="form-control" name="sexo" wire:model="sexo">
                                        <option>--- Seleccione ---</option>
                                        @foreach ($generos as $genero)
                                            <option value="{{ $genero->codigo_v }}">{{ $genero->descri_v }}</option>
                                        @endforeach
                                    </select>
                                    <x-input-error for="sexo" />
                                </div>

                                <div class="col-md-4">
                                    <x-label value="Fecha de nacimiento" />
                                    <x-adminlte-input type="date" name="fecha_nac" wire:model="fecha_nac"
                                        class="form-control" autocomplete=off />
                                </div>
                                <div class="col-md-4">
                                    <x-label value="Estado Civil" />
                                    <select class="form-control" name="estado_civil" wire:model="estado_civil">
                                        <option>--- Seleccione ---</option>
                                        @foreach ($estadosCiviles as $estadoCivil)
                                            <option value="{{ $estadoCivil->codigo_v }}">{{ $estadoCivil->descri_v }}
                                            </option>
                                        @endforeach
                                    </select>
                                    <x-input-error for="estado_civil" />
                                </div>

                                <div class="col-md-12">
                                    <x-label value="Dirección" />
                                    <x-adminlte-textarea name="direccion" wire:model="direccion" class="form-control"
                                        autocomplete=off />
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="form-row ">
                                <legend class="mt-2 mb-0 text-primary">Información de contacto</legend>

                                <div class="col-md-3">
                                    <div class="form-group">
                                        <x-label for="telefono_cel" value="{{ __('Teléfono personal') }}" />
                                        <x-input id="telefono_cel" class="form-control" type="number"
                                            name="telefono_cel" wire:model="telefono_cel" />
                                        <x-input-error for="telefono_cel" class="mt-2" />
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <div class="form-group">
                                        <x-label for="telefono_hab" value="{{ __('Teléfono habitacion') }}" />
                                        <x-input id="telefono_hab" class="form-control" type="number"
                                            name="telefono_hab" wire:model="telefono_hab" />
                                        <x-input-error for="telefono_hab" class="mt-2" />
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <x-label for="telefono_cont" value="{{ __('Teléfono contacto') }}" />
                                        <x-input id="telefono_cont" class="form-control" type="number"
                                            name="telefono_cont" wire:model="telefono_cont" />
                                        <x-input-error for="telefono_cont" class="mt-2" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="form-row ">
                                <div class="col-md">
                                    <div class="form-group">
                                        <x-label for="ocupacion" value="{{ __('Ocupación') }}" />
                                        <x-adminlte-textarea rows="3" name="ocupacion" class="form-control"
                                            wire:model="ocupacion" autocomplete="off" />
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </x-slot>
            <x-slot name="footer">
                <x-button class="mt-2 btn btn-secondary" wire:click="$set('modalCreatePersonaAutorizada',false)">
                    {{ __('Cerrar') }}
                </x-button>
                <x-button class="mt-2 btn btn-primary" wire:click.prevent="save"><i class="fas fa-save"></i>
                    {{ __('Guardar') }}
                </x-button>
            </x-slot>
        </form>
    </x-dialog-modal>
</div>
