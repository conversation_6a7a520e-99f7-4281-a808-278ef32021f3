<?php

namespace Modules\Backend\Livewire\ServicioFunerario\Contratos\Pages;

use App\Models\Contrato;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ServicioFunerarioExport;
use App\Exports\AsociadosGrupoFamiliarExport;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Rappasoft\LaravelLivewireTables\Views\Column;
use App\Exports\ServicioFunerarioFamiliaresExport;
use Rappasoft\LaravelLivewireTables\DataTableComponent;
use Rappasoft\LaravelLivewireTables\Views\Filters\SelectFilter;
use Rappasoft\LaravelLivewireTables\Views\Filters\DateFilter;
use Livewire\Attributes\On;
use Jantinnerezo\LivewireAlert\LivewireAlert;

#[Lazy]
class ListContratos extends DataTableComponent
{
    use LivewireAlert;

    public string $tableName = 'servicio_funerario_contratos';
    public array $asociados = [];

    public $columnSearch = [
        'cedula_soc' => null,
        'periodo' => null,
    ];

    // Definimos los listeners para Livewire 3
    protected $listeners = ['actualizarContratosMasivos' => 'procesarContratosMasivos'];

    public function configure(): void
    {
        $this->setPrimaryKey('id')
            ->setTableRowUrl(function ($row) {
                return route('admin.servicio-funerario.contratos.show', $row);
            })->setDefaultSort('id', 'desc')
            ->setFilterLayoutSlideDown()
            ->setAdditionalSelects(['servicio_funerario_contratos.*'])
            ->setConfigurableAreas([
                'toolbar-right-start' => 'backend::livewire.servicio-funerario.contratos.includes.update-button',
            ]);
    }

    // Método para procesar contratos masivos
    public function procesarContratosMasivos()
    {
        try {
            // Crear una instancia del componente FormMetodoPago
            $formMetodoPago = new \Modules\Frontend\Livewire\ServicioFunerario\Contratos\Components\Form\FormMetodoPago();

            // Ejecutar el método contratoMasivosCaprof directamente
            $formMetodoPago->contratoMasivosCaprof();

            // Mostrar mensaje de éxito
            $this->alert('success', 'Contratos actualizados exitosamente', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'timerProgressBar' => true,
                'showConfirmButton' => true,
            ]);

            // Refrescar la tabla
            $this->refreshTable();
        } catch (\Exception $e) {
            // Mostrar mensaje de error
            $this->alert('error', 'Error al actualizar los contratos: ' . $e->getMessage(), [
                'position' => 'center',
                'timer' => 5000,
                'toast' => false,
                'timerProgressBar' => true,
                'showConfirmButton' => true,
            ]);
        }
    }

    public function refreshTable()
    {
        // Método para refrescar la tabla
        $this->resetPage();
    }

    public function filters(): array
    {
        // Obtener los periodos disponibles desde la base de datos
        $periodos = DB::table('servicio_funerario_contratos')
            ->select('periodo')
            ->distinct()
            ->orderBy('periodo', 'desc')
            ->pluck('periodo')
            ->toArray();

        // Crear un array asociativo para las opciones del filtro
        $periodosOptions = ['' => 'Todos'];
        foreach ($periodos as $periodo) {
            $periodosOptions[$periodo] = $periodo;
        }

        // Crear un array asociativo para los valores de las píldoras del filtro
        $periodosPillValues = [];
        foreach ($periodos as $periodo) {
            $periodosPillValues[$periodo] = $periodo;
        }

        return [
            SelectFilter::make('Estatus')
                ->setFilterPillTitle('Estatus')
                ->setFilterPillValues([
                    '1' => 'Activo',
                    '2' => 'Anulado',
                    '3' => 'Suspendido',
                ])
                ->options([
                    '' => 'Todos',
                    '1' => 'Activo',
                    '2' => 'Anulado',
                    '3' => 'Suspendido',
                ])
                ->filter(function(Builder $builder, string $value) {
                    $builder->where('servicio_funerario_contratos.estatus', $value);
                }),

            SelectFilter::make('Periodo')
                ->setFilterPillTitle('Periodo')
                ->setFilterPillValues($periodosPillValues)
                ->options($periodosOptions)
                ->filter(function(Builder $builder, string $value) {
                    $builder->where('servicio_funerario_contratos.periodo', $value);
                }),

            DateFilter::make('Fecha Desde')
                ->config([
                    'min' => '2020-01-01',
                    'max' => date('Y-m-d'),
                ])
                ->filter(function(Builder $builder, string $value) {
                    $builder->whereDate('servicio_funerario_contratos.created_at', '>=', $value);
                }),

            DateFilter::make('Fecha Hasta')
                ->config([
                    'min' => '2020-01-01',
                    'max' => date('Y-m-d'),
                ])
                ->filter(function(Builder $builder, string $value) {
                    $builder->whereDate('servicio_funerario_contratos.created_at', '<=', $value);
                }),
        ];
    }

    public function columns(): array
    {
        return [
            Column::make("ID", "id")
                ->sortable()
                ->searchable(),
            Column::make("Cedula", "cedula_soc")
                ->sortable()
                ->searchable(),
            Column::make("Periodo", "periodo")
                ->sortable()
                ->searchable(),
            Column::make("Nombres", "asociado.nombres")
                ->sortable()
                ->searchable(),
            Column::make("Apellidos", "asociado.apellidos")
                ->sortable()
                ->searchable(),
            Column::make("Monto del contrato", "monto_total_contrato")
                ->sortable()
                ->searchable(),
            Column::make("Ubicacion", "asociado.ubicacion.descri_v")
                ->sortable()
                ->searchable(),
            Column::make("Estatus", "estatus")
                ->format(function ($value, $row, Column $column) {
                    return "<span class='badge badge-{$row->condicion->color}'><i class='{$row->condicion->icono}'></i> {$row->condicion->descripcion}</span>";
                })->sortable()
                ->searchable(
                    fn (Builder $query, $searchTerm) => $query->orWhere('config_tools.descripcion', 'like', '%' . $searchTerm . '%')->with('condicion'),
                )->html(),
            Column::make("Fecha", "created_at")
                ->sortable()
                ->searchable(),
        ];
    }

    public function bulkActions(): array
    {
        return [
            'export' => 'Exportar Contratos',
            'exportarFamiliares' => 'Exportar Familiares',
            'exportarAsociadosGrupoFamiliar' => 'Exportar Asociados con Grupo Familiar',
        ];
    }

    public function export()
    {
        ini_set('memory_limit', '-1');

        // Obtener los filtros aplicados
        $filters = [
            'estatus' => $this->getFilterByKey('estatus'),
            'periodo' => $this->getFilterByKey('periodo'),
            'fecha_desde' => $this->getFilterByKey('fecha-desde'),
            'fecha_hasta' => $this->getFilterByKey('fecha-hasta'),
        ];

        $selectedRows = $this->getSelected();

        // Si hay filas seleccionadas, exportar solo esas
        // Si no, exportar todas las filas con los filtros aplicados
        $this->clearSelected();

        return Excel::download(
            new ServicioFunerarioExport($selectedRows, count($selectedRows) > 0 ? null : $filters),
            'contratos_' . date('Y-m-d') . '.xlsx'
        );
    }

    public function exportarFamiliares()
    {
        ini_set('memory_limit', '-1');

        // Obtener los filtros aplicados
        $filters = [
            'estatus' => $this->getFilterByKey('estatus'),
            'periodo' => $this->getFilterByKey('periodo'),
            'fecha_desde' => $this->getFilterByKey('fecha-desde'),
            'fecha_hasta' => $this->getFilterByKey('fecha-hasta'),
        ];

        $selectedRows = $this->getSelected();

        // Si hay filas seleccionadas, exportar solo esas
        // Si no, exportar todas las filas con los filtros aplicados
        $this->clearSelected();

        try {
            return Excel::download(
                new ServicioFunerarioFamiliaresExport($selectedRows, count($selectedRows) > 0 ? null : $filters),
                'contratos_familiares_' . date('Y-m-d') . '.xlsx'
            );
        } catch (\Exception $e) {
            // Registrar el error para depuración
            \Log::error('Error al exportar familiares: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());

            // Mostrar mensaje de error al usuario
            $this->alert('error', 'Error al exportar: ' . $e->getMessage(), [
                'position' => 'center',
                'timer' => 5000,
                'toast' => false,
                'timerProgressBar' => true,
                'showConfirmButton' => true,
            ]);

            return null;
        }
    }

    public function exportarAsociadosGrupoFamiliar()
    {
        ini_set('memory_limit', '-1');

        try {
            return Excel::download(
                new AsociadosGrupoFamiliarExport(),
                'asociados_grupo_familiar_' . date('Y-m-d') . '.xlsx'
            );
        } catch (\Exception $e) {
            // Registrar el error para depuración
            \Log::error('Error al exportar asociados con grupo familiar: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());

            // Mostrar mensaje de error al usuario
            $this->alert('error', 'Error al exportar: ' . $e->getMessage(), [
                'position' => 'center',
                'timer' => 5000,
                'toast' => false,
                'timerProgressBar' => true,
                'showConfirmButton' => true,
            ]);

            return null;
        }
    }

    public function builder(): Builder
    {
        return Contrato::query()
            ->join('config_tools', 'servicio_funerario_contratos.estatus', '=', 'config_tools.codigo')
            ->where('config_tools.modulo', 'servicio-funerario.contratos')
            ->where('config_tools.referencia', 'estatus')
            ->with('asociado')
            ->with('asociado.ubicacion')->where('ref_v','D')
            ->orderBy('id', 'desc');
    }

    #[On('refreshContratos')]
    public function refreshContratos()
    {
        // Refrescar los datos sin recargar la página
        $this->resetPage();
    }
}
