<?php

require_once 'vendor/autoload.php';

use App\Exports\ContratosVicerrectoradoEstadisticasExport;
use Maatwebsite\Excel\Facades\Excel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    echo "Testing improved ContratosVicerrectoradoEstadisticasExport class...\n";
    
    // Create export instance
    $export = new ContratosVicerrectoradoEstadisticasExport();
    echo "Export class instantiated successfully\n";
    
    // Test collection method
    $collection = $export->collection();
    echo "Collection size: " . $collection->count() . "\n";
    
    // Test headings
    $headings = $export->headings();
    echo "Headings: " . implode(', ', $headings) . "\n\n";
    
    // Show detailed statistics
    echo "ESTADÍSTICAS DETALLADAS POR VICERRECTORADO:\n";
    echo str_repeat("=", 140) . "\n";
    printf("%-35s %-8s %-8s %-8s %-12s %-12s %-15s %-12s\n", 
        "VICERRECTORADO", "TOTAL", "ACTIVOS", "ANULADOS", "SUSPENDIDOS", "C.BÁSICA", "C.ADICIONAL", "PORCENTAJE");
    echo str_repeat("=", 140) . "\n";
    
    foreach ($collection as $row) {
        $mapped = $export->map($row);
        printf("%-35s %-8s %-8s %-8s %-12s %-12s %-15s %-12s\n", 
            substr($mapped[0], 0, 34), // Truncar nombre si es muy largo
            $mapped[1], 
            $mapped[2], 
            $mapped[3], 
            $mapped[4], 
            $mapped[5], 
            $mapped[6], 
            $mapped[7]
        );
    }
    
    echo str_repeat("=", 140) . "\n";
    
    // Test Excel generation
    echo "\nTesting Excel generation with new columns...\n";
    $filename = 'test_estadisticas_mejoradas_' . date('Y-m-d_H-i-s') . '.xlsx';
    $filepath = storage_path('app/' . $filename);
    
    Excel::store($export, $filename);
    
    if (file_exists($filepath)) {
        $filesize = filesize($filepath);
        echo "Excel file generated successfully!\n";
        echo "File: {$filepath}\n";
        echo "Size: " . number_format($filesize) . " bytes\n";
        
        // Clean up test file
        unlink($filepath);
        echo "Test file cleaned up.\n";
    } else {
        echo "Error: Excel file was not created.\n";
    }
    
    echo "\nImproved statistics test completed successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
