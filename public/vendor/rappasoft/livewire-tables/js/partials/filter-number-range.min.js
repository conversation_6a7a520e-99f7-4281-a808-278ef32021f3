export function nrf(){Alpine.data("numberRangeFilter",(t,i,e,a,s)=>({allFilters:t.entangle("filterComponents",!1),originalMin:0,originalMax:100,filterMin:0,filterMax:100,currentMin:0,currentMax:100,hasUpdate:!1,wireValues:t.entangle("filterComponents."+i,!1),defaultMin:a.minRange,defaultMax:a.maxRange,restrictUpdates:!1,updateStyles(){let t=document.getElementById(e),i=document.getElementById(s+"-min"),a=document.getElementById(s+"-max");t.style.setProperty("--value-a",i.value),t.style.setProperty("--text-value-a",JSON.stringify(i.value)),t.style.setProperty("--value-b",a.value),t.style.setProperty("--text-value-b",JSON.stringify(a.value))},setupWire(){void 0!==this.wireValues?(this.filterMin=this.originalMin=void 0!==this.wireValues.min?this.wireValues.min:this.defaultMin,this.filterMax=this.originalMax=void 0!==this.wireValues.max?this.wireValues.max:this.defaultMax):(this.filterMin=this.originalMin=this.defaultMin,this.filterMax=this.originalMax=this.defaultMax),this.updateStyles()},allowUpdates(){this.updateWire()},updateWire(){let t=parseInt(this.filterMin),i=parseInt(this.filterMax);(t!=this.originalMin||i!=this.originalMax)&&(i<t&&(this.filterMin=i,this.filterMax=t),this.hasUpdate=!0,this.originalMin=t,this.originalMax=i),this.updateStyles()},updateWireable(){this.hasUpdate&&(this.hasUpdate=!1,this.wireValues={min:this.filterMin,max:this.filterMax},t.set("filterComponents."+i,this.wireValues))},init(){this.setupWire(),this.$watch("allFilters",t=>this.setupWire())}}))}export default nrf;