<?php

require_once 'vendor/autoload.php';

use App\Exports\AsociadosGrupoFamiliarExport;
use Maatwebsite\Excel\Facades\Excel;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    echo "Testing Excel generation...\n";
    
    // Create a small test export (limit to first 10 associates to speed up testing)
    $export = new AsociadosGrupoFamiliarExport();
    
    // Test that we can create the Excel file
    $filename = 'test_asociados_grupo_familiar_' . date('Y-m-d_H-i-s') . '.xlsx';
    $filepath = storage_path('app/' . $filename);
    
    // Generate the Excel file
    Excel::store($export, $filename);
    
    if (file_exists($filepath)) {
        $filesize = filesize($filepath);
        echo "Excel file generated successfully!\n";
        echo "File: {$filepath}\n";
        echo "Size: " . number_format($filesize) . " bytes\n";
        
        // Clean up test file
        unlink($filepath);
        echo "Test file cleaned up.\n";
    } else {
        echo "Error: Excel file was not created.\n";
    }
    
    echo "Excel generation test completed successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
