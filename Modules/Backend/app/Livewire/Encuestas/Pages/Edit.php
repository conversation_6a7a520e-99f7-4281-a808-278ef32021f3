<?php

namespace Modules\Backend\Livewire\Encuestas\Pages;

use Livewire\Component;
use App\Models\Encuesta;

class Edit extends Component
{
    const PERMISSIONS = [
        'edit' => 'admin.encuestas.edit',
    ];
    public Encuesta $encuesta;


    public function update()
    {
        $this->validate();

        if ($this->encuesta->id) {
            $record = Encuesta::find($this->encuesta->id);
            $record->update([
                'titulo' => $this->titulo,
                'descripcion' => $this->descripcion,
                'fecha_inicio' => $this->fecha_inicio,
                'fecha_fin' => $this->fecha_fin,
                'publicado' => $this->publicado
            ]);
            $this->alert('success', 'Encuesta actualizada con exito');
        }
    }


    public function render()
    {
        return view('backend::livewire.encuestas.pages.edit');
    }
}
